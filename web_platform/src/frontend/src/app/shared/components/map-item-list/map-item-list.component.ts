import { Component, ChangeDetectorRef, ChangeDetectionStrategy, Input, Output, EventEmitter } from '@angular/core';
import { MapItem, MemberItem } from '@services/dashboard.service';
import { OdxToastCustomService } from '@services/odx-toast-custom.service';
@Component({
  selector: 'map-item-list',
  templateUrl: './map-item-list.component.html',
  styleUrls: ['./map-item-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MapItemListComponent {
  @Input() items: MapItem[] = [];
  @Input() type: string = 'normal';
  @Output() selectItemChanged: EventEmitter<MapItem> = new EventEmitter();
  @Output() hideOrOpenGroup: EventEmitter<MapItem> = new EventEmitter();
  @Output() selectMemberChanged: EventEmitter<MemberItem> = new EventEmitter();

  constructor(
    private cdRef: ChangeDetectorRef,
    private msgSrv: OdxToastCustomService,
  ) {
    
  }
  public selectItem(mapItem: MapItem): void {
    this.selectItemChanged.emit(mapItem);
  }

  public selectMember( memberItem: MemberItem): void {
    this.selectMemberChanged.emit(memberItem);
  }

  public hide(event: MouseEvent, mapItem: MapItem): void {
    event.stopPropagation();
    mapItem.team_select = true
    this.hideOrOpenGroup.emit(mapItem);
  }
  public open(event: MouseEvent, mapItem: MapItem): void {
    event.stopPropagation();
    mapItem.team_select = false
    this.hideOrOpenGroup.emit(mapItem);
  }
  // 列表项的唯一标识
  trackItemById(index: number, item: any): string {
    return item.type + '_' + item.id;
  }
  // 报警项的唯一标识
  trackAlarmItemById = (mapItem: MapItem) => (index: number, item: any) => {
    return `${mapItem.identifier}_${item.name}`;
  }

  // 遥测数据的唯一标识
  trackByTelemetryName(index: number, telemetry: any): string {
    return telemetry.name;
  }

  // 获取有效的报警值数量
  getAlarmValueCount(telemetryList: any[]): number {
    if (!telemetryList) return 0;
    return telemetryList.filter(telemetry =>
      (telemetry.name !== 'fallAlarm' && telemetry.name !== 'activeAlarm' && telemetry.value && telemetry.status !== 'normal') ||
      ((telemetry.name === 'fallAlarm' || telemetry.name === 'activeAlarm') && telemetry.status === 'alarm')
    ).length;
  }
}
