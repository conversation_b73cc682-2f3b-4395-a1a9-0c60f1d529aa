@import "../../../../styles.scss";

.list {
  .map-item {
    border-bottom: 1px solid var(--device-detail-border-color);
    cursor: pointer;
    position: relative;

    ::ng-deep .ant-collapse-content-box {
      padding: 0;
    }

    ::ng-deep .ant-collapse-content {
      border: 0;
    }

    .map-item-info {
      color: var(--odx-c-text);

      .item-header {
        font-weight: 600;
        font-size: calc($baseFont * 14 / 24);
        height: calc($baseFont * 18 / 24);
        font-family: "DraegerPangeaText-SemiBold";
        overflow: hidden;

        ::ng-deep .odx-icon {
          font-size: calc($baseFont * 16 / 24);
          height: calc($baseFont * 18 / 24);
        }
      }

      .text-item {
        font-size: calc($baseFont * 11 / 24);
        font-family: "DraegerPangeaText-Regular";
        font-weight: 400;
        margin-top: calc($marginBase * 4 / 24);

        ::ng-deep .odx-icon {
          font-size: calc($baseFont * 14 / 24);
          height: calc($baseFont * 14 / 24);
        }

        span {
          display: inline-block;
          color: var(--odx-chip-color);
          line-height: calc($baseFont * 14 / 24);
          margin-right: calc($marginBase * 10 / 24);
        }
      }
    }

    &:last-child {
      border-bottom: none;
    }

    .map-item-content {
      .member-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: calc($marginBase * 26 / 24);
        padding-top: calc($marginBase * 8 / 24);
        padding-bottom: calc($marginBase * 8 / 24);
        padding-right: calc($marginBase * 10 / 24);
        font-size: calc($baseFont * 13 / 24);
        font-family: "DraegerPangeaText-Regular";
        font-weight: 400;

        .member-icon {
          ::ng-deep .odx-icon {
            font-size: calc($baseFont * 24 / 24);
            margin-right: calc($marginBase * 4 / 24);
          }
        }

        .device-icon {
          ::ng-deep .odx-icon {
            font-size: calc($baseFont * 16 / 24);
            margin: 0 calc($marginBase * 2 / 24);
          }
        }

        .device-item {
          background: none;
          margin: 0 calc($marginBase * 2 / 24);
          padding: 0;
          min-width: calc($baseFont * 20 / 24);
          line-height: calc($baseFont * 20 / 24);
          ::ng-deep .odx-chip__content {
            display: flex;
          }
        }

        .alarm-item {
          background: #fff;
          color: var(--device-alarm-color);

          ::ng-deep .odx-icon {
            color: var(--device-alarm-color) !important;
          }
        }

        .warning-item {
          background: var(--blue-700);
          color: var(--device-error-color);

          ::ng-deep .odx-icon {
            color: var(--device-error-color) !important;
          }
        }

        .offline-item {
          background: #D9D9D9;

          ::ng-deep .odx-icon {
            color: var(--blue-700) !important;
          }
        }

        .alarm-value-list {
          display: inline-block;
          max-width: 100%;
          height: calc($baseFont * 20 / 24);
          overflow: hidden;
          position: relative;

          .scroll-container {
            display: flex;
            flex-direction: column;

            // 默认情况下有滚动动画
            animation: scrollVertical 12s linear infinite;
          }

          // 当只有一个报警值时，不显示滚动动画
          &.single-value .scroll-container {
            animation: none;
          }

          .alarm-value {
            display: block;
            white-space: nowrap;
            line-height: calc($baseFont * 20 / 24);
            height: calc($baseFont * 20 / 24);
            flex-shrink: 0;
            margin-bottom: calc($marginBase * 2 / 24);

            // 确保图标和文字垂直居中
            display: flex;
            align-items: center;

            ::ng-deep .odx-icon {
              margin-right: calc($marginBase * 2 / 24);
            }
          }

          // 鼠标悬停时暂停滚动
          &:hover {
            .scroll-container {
              animation-play-state: paused;
            }
          }
        }

        @keyframes scrollVertical {
          0% {
            transform: translateY(0);
          }
          100% {
            transform: translateY(-100%);
          }
        }
      }

      .alarm-member {
        color: #fff;
        background: var(--device-alarm-color);

        .device-icon {
          ::ng-deep .odx-icon {
            color: #fff;
          }
        }
      }

      .selected-member {
        background: #E9EEF4;
      }

      .warning-member {
        background: var(--device-error-color);
      }

      .selected-member.alarm-member {
        background: var(--device-alarm-color-even);
      }

      .selected-member.warning-member {
        background: var(--device-error-color-even);
      }
    }
  }

  .selected-item {
    background: var(--map-item-active-bg-color);
  }

  .alarm-item {
    background: var(--device-alarm-color);

    .map-item-info {
      color: #fff;

      .text-item {
        span {
          color: var(--device-alarm-active-odx-chip-color);
        }
      }
    }

    ::ng-deep .ant-collapse-extra .odx-icon {
      color: #fff;
    }
  }

  .warning-item {
    background: var(--device-error-color);
  }

  .selected-item.alarm-item {
    background: var(--device-alarm-color-even);
  }

  .selected-item.warning-item {
    background: var(--device-error-color-even);
  }

}