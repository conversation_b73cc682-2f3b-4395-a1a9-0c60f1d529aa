import { Component, OnInit, ViewChild, ElementRef, Input, Output, EventEmitter, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Theme, ThemingService } from '@odx/angular/theming';
import { defaultMapConfig } from '@shared';
import { DeviceItem } from '@services/dashboard.service';
declare var AMap: any;
@Component({
  selector: 'app-device-map',
  templateUrl: './device-map.component.html',
  styleUrls: ['./device-map.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DeviceMapComponent implements OnInit {
  @ViewChild('map', { static: true }) map!: ElementRef;
  @Input() set _mapData(value: MapData[]) {
    this.mapData = value;
    if (value.length === 0) {
      this.markerRenderNum = 0;

    } else {
      this.markerRenderNum += 1;

    }
    this.updateMapMakers();
  }
  mapData: MapData[] = [];
  @Input() settings: TooltipSettings = {
    tooltipFontSize: '12px',
    tooltipFontColor: '#333'
  }
  @Output() selectedUserChanged: EventEmitter<SelectPoint> = new EventEmitter();
  @Output() viewTIC: EventEmitter<any> = new EventEmitter();
  @Output() changeViewModel: EventEmitter<any> = new EventEmitter();

  @Input() mapLevel?: number;
  @Input() viewModel?: string = 'group';
  @Input() set updateMapCenter(updateMapCenter: boolean) {
    this._updateMapCenter = updateMapCenter;
    if (updateMapCenter) {
      this.updateMapMakers();
    }
  }
  @Output() updateMapCenterChange: EventEmitter<boolean> = new EventEmitter();

  gas?: boolean = false;
  fixed?: boolean = false;
  _updateMapCenter: boolean = false;
  mapInstance: any = null;
  mapMarkers: any[] = [];
  lang: string = "zh_cn";
  zoom: number = 14;
  // 地图主题在系统主题的对应样式
  mapStyles = {
    light: 'amap://styles/whitesmoke',
    dark: 'amap://styles/grey'
  };
  mapStyle: string = 'amap://styles/whitesmoke';
  // 地图类型：地图、卫星图
  mapType: FormControl = new FormControl('satellite');
  readonly mapTypes = ['map', 'satellite'];
  // 卫星图对应的图层
  satelliteLayer: any;
  theme!: Theme;
  // 地图聚合点对应的实例
  cluster: any;
  // 存储连接线和辅助元素
  private connectionElements: any[] = [];
  // 当前选中的点
  selectedPoint?: [number, number];
  // 当前定位点更新次数
  markerRenderNum: number = 0;
  // 防重叠配置
  private overlapConfig = {
    minDistance: 50, // 最小距离（像素）
    maxOffset: 80,   // 最大偏移距离
    fanRadius: 60,   // 扇形展开半径
    enableSmartOffset: true, // 启用智能偏移
    enableFanLayout: true    // 启用扇形布局
  };
  // 标尺控件实例
  private scaleControl: any;
  constructor(
    private themingService: ThemingService,
    private cdRef: ChangeDetectorRef,

  ) { }

  ngOnInit(): void {
    // 根据主题更改地图主题
    this.themingService.theme$.subscribe(theme => {
      this.theme = theme;
      this.mapStyle = this.mapStyles[theme];
      this.mapInstance?.setMapStyle(this.mapStyle);
      this.updateMapMakers();
    });
    setTimeout(() => {
      this.initMap();
    }, 1000);
    // 地图和卫星图切换
    this.mapType.valueChanges.subscribe((type) => {
      if (type === 'satellite' && this.mapInstance) {
        this.satelliteLayer = new AMap.TileLayer.Satellite();
        this.mapInstance.add(this.satelliteLayer);
      } else {
        if (this.satelliteLayer) {
          this.mapInstance.remove(this.satelliteLayer);
        }
      }
      this.updateMapMakers();
    });
  }
  fixedChange(event: boolean) {
    this.fixed = event;
    this.updateMapMakers();
  }

  gasChange(event: boolean) {
    this.gas = event;
    this.updateMapMakers();
  }

  changeModel() {
    this.changeViewModel.emit()
  }

  viewTic() {
    this.viewTIC.emit()
  }
  // 缩放地图
  changeMap(type: string) {
    if (type == 'min') {
      this.zoom = this.zoom <= 3 ? 3 : this.zoom - 1
    } else {
      this.zoom = this.zoom >= 20 ? 20 : this.zoom + 1
    }
    console.log("缩放地图", this.zoom)
    this.mapInstance.setZoom(this.zoom)
  }
  //创建地图
  private initMap(): void {
    const lng = defaultMapConfig.lng;
    const lat = defaultMapConfig.lat;
    this.mapInstance = new AMap.Map(this.map.nativeElement, {
      center: [lng, lat],
      resizeEnabled: true,
      zoom: this.zoom,
      mapStyle: this.mapStyle,
      lang: 'zh_cn',
      zooms: defaultMapConfig.zooms,
      expandZoomRange: true,
    });

    // 等待地图完全加载后添加标尺控件
    this.mapInstance.on('complete', () => {
      console.log('地图加载完成，开始添加标尺控件');
      this.satelliteLayer = new AMap.TileLayer.Satellite();
      this.mapInstance.add(this.satelliteLayer);
      this.addScaleControl();
    });
    this.updateMapMakers();
  }

  // 添加标尺控件
  private addScaleControl(): void {
    if (!this.mapInstance) {
      console.warn('地图实例不存在，无法添加标尺控件');
      return;
    }

    // 检查 AMap.Scale 是否可用
    if (typeof AMap === 'undefined' || !AMap.Scale) {
      console.error('AMap.Scale 控件不可用，请检查高德地图API是否正确加载');
      return;
    }

    try {
      console.log('开始创建标尺控件...');

      // 创建标尺控件
      this.scaleControl = new AMap.Scale({
        position: 'RB', // 左下角位置 (LeftBottom)
        offset: new AMap.Pixel(10, 10), // 偏移量
        visible: true // 是否可见
      });

      console.log('标尺控件创建成功，准备添加到地图...');

      // 将标尺控件添加到地图
      this.mapInstance.addControl(this.scaleControl);
      console.log('标尺控件已成功添加到地图');

    } catch (error) {
      console.error('添加标尺控件失败:', error);
      if (error instanceof Error) {
        console.error('错误详情:', error.message);
      }
    }
  }

  // 计算两点之间的像素距离
  private calculatePixelDistance(pos1: [number, number], pos2: [number, number]): number {
    if (!this.mapInstance) return 0;

    const pixel1 = this.mapInstance.lngLatToContainer(pos1);
    const pixel2 = this.mapInstance.lngLatToContainer(pos2);

    const dx = pixel1.x - pixel2.x;
    const dy = pixel1.y - pixel2.y;

    return Math.sqrt(dx * dx + dy * dy);
  }

  // 检测并处理重叠的marker
  private processOverlappingMarkers(markersData: Array<{ item: MapData, index: number }>): Array<{ item: MapData, index: number, offset: [number, number] }> {
    const processedMarkers: Array<{ item: MapData, index: number, offset: [number, number] }> = [];

    markersData.forEach((markerData, i) => {
      const currentPos: [number, number] = [
        Number(markerData.item.center.longitude),
        Number(markerData.item.center.latitude)
      ];

      // 查找与当前marker重叠的其他markers
      const overlappingMarkers = processedMarkers.filter(processed => {
        const processedPos: [number, number] = [
          Number(processed.item.center.longitude),
          Number(processed.item.center.latitude)
        ];
        return this.calculatePixelDistance(currentPos, processedPos) < this.overlapConfig.minDistance;
      });

      let offset: [number, number] = [0, 0];

      if (overlappingMarkers.length > 0 && this.overlapConfig.enableSmartOffset) {
        if (this.overlapConfig.enableFanLayout && overlappingMarkers.length >= 2) {
          // 扇形布局
          offset = this.calculateFanOffset(overlappingMarkers.length, i);
        } else {
          // 智能偏移
          offset = this.calculateSmartOffset(currentPos, overlappingMarkers);
        }
      }

      processedMarkers.push({
        item: markerData.item,
        index: markerData.index,
        offset
      });
    });

    return processedMarkers;
  }

  // 计算扇形偏移
  private calculateFanOffset(totalOverlapping: number, currentIndex: number): [number, number] {
    const angle = (currentIndex * 360 / totalOverlapping) * (Math.PI / 180);
    const radius = this.overlapConfig.fanRadius;

    const offsetX = Math.cos(angle) * radius;
    const offsetY = Math.sin(angle) * radius;

    return [offsetX, offsetY];
  }

  // 计算智能偏移
  private calculateSmartOffset(currentPos: [number, number], overlappingMarkers: Array<{ item: MapData, offset: [number, number] }>): [number, number] {
    let bestOffset: [number, number] = [0, 0];
    let maxMinDistance = 0;

    // 尝试8个方向的偏移
    const directions = [
      [1, 0], [1, 1], [0, 1], [-1, 1],
      [-1, 0], [-1, -1], [0, -1], [1, -1]
    ];

    for (const direction of directions) {
      const testOffset: [number, number] = [
        direction[0] * this.overlapConfig.maxOffset,
        direction[1] * this.overlapConfig.maxOffset
      ];

      // 计算这个偏移位置到所有已存在marker的最小距离
      let minDistance = Infinity;
      for (const existing of overlappingMarkers) {
        const existingPos: [number, number] = [
          Number(existing.item.center.longitude),
          Number(existing.item.center.latitude)
        ];
        const distance = this.calculatePixelDistance(
          [currentPos[0] + testOffset[0] / 100000, currentPos[1] + testOffset[1] / 100000],
          [existingPos[0] + existing.offset[0] / 100000, existingPos[1] + existing.offset[1] / 100000]
        );
        minDistance = Math.min(minDistance, distance);
      }

      if (minDistance > maxMinDistance) {
        maxMinDistance = minDistance;
        bestOffset = testOffset;
      }
    }

    return bestOffset;
  }

  // 清理连接线和辅助元素
  private clearConnectionElements(): void {
    this.connectionElements.forEach(element => {
      this.mapInstance.remove(element);
    });
    this.connectionElements = [];
  }

  // 添加连接线显示偏移关系
  private addConnectionLine(originalPos: [number, number], offsetPos: [number, number], item: MapData): void {
    const lineColor = this.theme === 'dark' ? '#666666' : '#cccccc';
    const polyline = new AMap.Polyline({
      path: [originalPos, offsetPos],
      strokeColor: lineColor,
      strokeWeight: 1,
      strokeStyle: 'dashed',
      strokeOpacity: 0.6,
      zIndex: 1 // 确保线条在marker下方
    });

    this.mapInstance.add(polyline);
    this.connectionElements.push(polyline);

    // 在原始位置添加一个小圆点
    const originalMarker = new AMap.Marker({
      position: originalPos,
      content: `<div style="width: 6px; height: 6px; background: ${lineColor}; border-radius: 50%; border: 1px solid #fff;"></div>`,
      offset: new AMap.Pixel(-3, -3),
      zIndex: 2
    });

    this.mapInstance.add(originalMarker);
    this.connectionElements.push(originalMarker);
  }

  // 公共方法：设置防重叠配置
  public setOverlapConfig(config: Partial<typeof this.overlapConfig>): void {
    this.overlapConfig = { ...this.overlapConfig, ...config };
    this.updateMapMakers(); // 重新渲染
  }

  // 公共方法：获取当前防重叠配置
  public getOverlapConfig(): typeof this.overlapConfig {
    return { ...this.overlapConfig };
  }

  // 绘制地图上的标点
  private updateMapMakers(): void {
    if (this.mapInstance && this.mapData?.length) {
      this.mapMarkers = [];
      this.cluster?.clearMarkers();
      this.selectedPoint = undefined;

      // 清理之前的连接线和辅助元素
      this.clearConnectionElements();

      // 准备marker数据并处理重叠
      const markersData = this.mapData.map((item, index) => ({ item, index }));
      const processedMarkers = this.processOverlappingMarkers(markersData);

      processedMarkers.forEach(({ item, index, offset }) => {
        const iconName = this.getIconName(item);
        const baClass = this.getClassName(item, 'pressure_gauge')
        const tttClass = this.getClassName(item, 'ttt')
        const ticClass = this.getClassName(item, 'tic')
        const pamClass = this.getClassName(item, 'gas_detector')
        let iconSize = 38;
        const markerLng = Number(item.center.longitude) + offset[0] / 100000;
        const markerLat = Number(item.center.latitude) + offset[1] / 100000;
        let marker;
        let content: string;
        // 正常状态直接使用图标作为marker
        if (!this.fixed && item.type == 'device') {
          return;
        }
        if (item.type == 'device') {
          iconSize = item.selected ? 48 : 30;
          // 报警或者故障状态加上动画
          if (item.status === 'alarm' || item.status === 'warning' || item.status === 'fault') {
            content = `<div class="radar-marker ${iconSize == 48 ? 'big' : ''}-radar-marker" id='marker_device_${item.id}'>
                        <img src="./assets/img/map_icon/${iconName}.svg" style="width: ${iconSize}px">
                        <div class="wrapper">
                          <div class="pulse p1 ${item.status}-pulse"></div>
                          <div class="pulse p2 ${item.status}-pulse"></div>
                        </div>
                      </div>`

          } else {
            // 正常状态直接使用图标作为marker
            content = `<div class="marker" id='marker_device_${item.id}'>
                          <img src="./assets/img/map_icon/${iconName}.svg" style="width: ${iconSize}px">
                      </div>`
          }
        } else {
          iconSize = item.selected ? 40 : 35;
          let height = item.selected ? 62 : 50;
          let center = item.selected ? 31 : 25;
          let width = item.selected ? 24 : 20;
          let length = item.selected ? 150 : 125.6;
          if (item.status != 'normal' && item.type != 'captain') {
            iconSize = item.selected ? 40 : 33;
          } else if (item.type == 'captain') {
            iconSize = item.selected ? 40 : 36;
          }
          if (this.gas) {
            console.log(item);
            console.log('====');
            content = `<div class="marker" id='marker_${item.id}'>
                          <div style="width: ${height}px; height: ${height}px" class="progress-ring" id="ring2">
                            <svg width="${height}" height="${height}">
                                <circle class="bg" cx="${center}" cy="${center}" r="${width}"/>
                                <circle class="bar ${item.selected ? 'select-bar' : ''}" cx="${center}" cy="${center}" r="${width}" stroke="#0091F7" style="stroke-dashoffset: ${length * item.gasValue}"/>
                            </svg>
                            <img class="progress-icon ${item.type == 'captain' ? 'progress-captain-icon' : ''}" src="./assets/img/map_icon/${iconName}.svg" style="height: ${iconSize}px">
                            <div class="${item.status == 'normal' ? '' : 'wrapper'}">
                              <div class="pulse p1 ${item.status}-pulse"></div>
                              <div class="pulse p2 ${item.status}-pulse"></div>
                            </div>
                          </div>
                          <p class="marker-name">${item.name}</p>
                          <div class="marker-device">`
            if (item.gateway == 'ttt') {
              content += `    <image class="device-icon ${tttClass}" src="${tttClass === 'device_warning' ? './assets/img/map_icon/communication-tool_warning.svg' : './assets/img/map_icon/communication-tool.svg'}" style="width: 24px">`
            } else if (item.gateway == 'smart') {
              content += `    <image class="device-icon ${tttClass}" src="${tttClass === 'device_warning' ? './assets/img/map_icon/smart_warning.svg' : './assets/img/map_icon/smart.svg'}" style="width: 24px">`
            }
            content += ` <div class="right-item">`
            item.devices.forEach((device) => {
              if (device.name == 'BA') {
                content += `<image class="device-icon ${baClass}" src="${baClass === 'device_alarm' ? './assets/img/map_icon/scba_alarm.svg' : './assets/img/map_icon/scba.svg'}" style="width:20px">`
              } else if (device.name == 'PAM') {
                content += `<image class="device-icon ${pamClass}" src="${pamClass === 'device_alarm' ? './assets/img/map_icon/xam-2000_alarm.svg' : './assets/img/map_icon/xam-2000.svg'}" style="width: 20px">`
              } else if (device.name == 'TIC') {
                content += `<image class="device-icon ${ticClass}" src="./assets/img/map_icon/ucf-6000-9000.svg" style="width: 20px">`
              }
            })
            content += ` </div>
                          </div>
                        </div>`
          } else {
            console.log(item)
            console.log('-------map marker------')
            iconSize = item.selected ? 50 : 40;
            content = `<div class="marker" id='marker_${item.id}'>
                            <div class="member-icon-wrapper">
                              <img class="member-icon" src="./assets/img/map_icon/${iconName}.svg" style="height: ${iconSize}px">
                              <div class="${item.status == 'normal' ? '' : 'wrapper'}">
                                <div class="pulse p1 ${item.status}-pulse"></div>
                                <div class="pulse p2 ${item.status}-pulse"></div>
                              </div>
                            </div>
                            <p class="marker-name">${item.name}</p>
                            <div class="marker-device">`
            if (item.gateway == 'ttt') {
              content += `    <image class="device-icon ${tttClass}" src="${tttClass === 'device_warning' ? './assets/img/map_icon/communication-tool_warning.svg' : './assets/img/map_icon/communication-tool.svg'}" style="width: 24px">`
            } else if (item.gateway == 'smart') {
              content += `    <image class="device-icon ${tttClass}" src="${tttClass === 'device_warning' ? './assets/img/map_icon/smart_warning.svg' : './assets/img/map_icon/smart.svg'}" style="width: 24px">`
            }
            content += ` <div class="right-item">`
            item.devices.forEach((device) => {
              if (device.name == 'BA') {
                content += `<image class="device-icon ${baClass}" src="${baClass === 'device_alarm' ? './assets/img/map_icon/scba_alarm.svg' : './assets/img/map_icon/scba.svg'}" style="width:20px">`
              } else if (device.name == 'PAM') {
                content += `<image class="device-icon ${pamClass}" src="${pamClass === 'device_alarm' ? './assets/img/map_icon/xam-2000_alarm.svg' : './assets/img/map_icon/xam-2000.svg'}" style="width: 20px">`
              } else if (device.name == 'TIC') {
                content += `<image class="device-icon ${ticClass}" src="./assets/img/map_icon/ucf-6000-9000.svg" style="width: 20px">`
              }
            })


            content += ` </div>
                          </div>
                        </div>`
          }
        }
        const showLevel = {
          'alarm': 10,
          'warning': 8,
          'normal': 7,
          'fault': 9
        };
        marker = new AMap.Marker({
          position: [markerLng, markerLat],
          content,
          offset: new AMap.Pixel(-20, -20),
          clickable: true,
          extData: {
            index: index,
            data: item,
            originalPosition: [Number(item.center.longitude), Number(item.center.latitude)],
            hasOffset: offset[0] !== 0 || offset[1] !== 0
          },
          zIndex: item.selected ? showLevel[item.status] + 1 : showLevel[item.status]
        });

        // 如果marker有偏移，添加连接线
        if (offset[0] !== 0 || offset[1] !== 0) {
          this.addConnectionLine(
            [Number(item.center.longitude), Number(item.center.latitude)],
            [markerLng, markerLat],
            item
          );
        }
        if (item.selected) {
          this.selectedPoint = [markerLng, markerLat];
        }
        marker.on('click', (event: { target: any }) => {
          console.log('========111111=')
          this.selectedUserChanged.emit({ type: item.type, id: item.id });
        })
        this.mapMarkers.push(marker);
      });
      const clusterIcon = this.theme === 'dark' ? `./assets/img/map_icon/group_black.svg` : `./assets/img/map_icon/group_light.svg`;
      // 创建聚合点
      this.cluster = new AMap.MarkerClusterer(this.mapInstance, this.mapMarkers, {
        gridSize: 50,
        renderClusterMarker: this.renderClusterMarker
      });
      this.cluster.on('click', (event: { target: any }) => {
        if (this.mapInstance.getZoom() == 18) {
          this.mapInstance.setZoom(20)
          this.zoom = 20
        }
      })
      // 只有updateMapCenter为true，或者首次渲染地图定位时更新地图中心点
      if (this.markerRenderNum === 1 || this._updateMapCenter) {
        if (this.selectedPoint) {
          let coords = this.mapInstance.getCenter();
          let [lang, lat] = Array.isArray(coords) ? this.mapInstance.getCenter() : [];
          if (coords && coords.lng && coords.lat) {
            lang = coords.lng;
            lat = coords.lat;
          }
          if (lang !== this.selectedPoint?.[0] && lat !== this.selectedPoint?.[1]) {
            console.log('asdadad')
            console.log(defaultMapConfig)
            this.zoom = defaultMapConfig.showCenterZoom;
            this.mapInstance.setZoomAndCenter(defaultMapConfig.showCenterZoom, this.selectedPoint);
          }
        } else {
          const firstMarker = this.mapData?.[0];
          const markerLng = Number(firstMarker.center.longitude);
          const markerLat = Number(firstMarker.center.latitude);
          this.mapInstance.setCenter([markerLng, markerLat]);
        }
        setTimeout(() => {
          this.updateMapCenterChange.emit(false);
        }, 500);
      }

    } else {
      this.cluster?.clearMarkers();
    }
  }

  // 根据内部Marker状态绘制聚合点的样式
  renderClusterMarker = (context: { count: number; markers: any[], marker: any; }) => {
    const markers = context.markers;
    const hasAlarm = !!markers.find(item => {
      const makerItemData = item.getExtData().data;
      return makerItemData.status === 'alarm';
    });
    const hasError = !!markers.find(item => {
      const makerItemData = item.getExtData().data;
      return makerItemData.status === 'warning' || makerItemData.status === 'fault';
    })
    let clusterIconBorderColor;
    let clusterIconBgColor;
    let size = 40;
    let fontColor;
    if (context.count > 100) {
      size = 80
    } else if (context.count > 10) {
      size = 54
    }
    if (hasAlarm) {
      clusterIconBorderColor = this.theme === 'dark' ? '#E6141E' : '#F30303';
      clusterIconBgColor = this.theme === 'dark' ? '#E6141E' : '#F30303';
      fontColor = this.theme === 'dark' ? '#FFFFFF' : '#FFFFFF';
    } else if (hasError) {
      clusterIconBorderColor = this.theme === 'dark' ? `#FFE600` : `#FFE600`;
      clusterIconBgColor = this.theme === 'dark' ? `#FFE600` : `#FFE600`;
      fontColor = this.theme === 'dark' ? '#FFFFFF' : '#002766';
    } else {
      clusterIconBorderColor = this.theme === 'dark' || this.mapType.value == 'satellite' ? `#D5E2F6` : `#002766`;
      clusterIconBgColor = this.theme === 'dark' || this.mapType.value == 'satellite' ? `#D5E2F6` : `#002766`;
      fontColor = this.theme === 'dark' || this.mapType.value == 'satellite' ? '#060A12' : '#FFFFFF';
    }
    const content = document.createElement('div');
    content.style.width = content.style.height = size + 'px';
    content.innerHTML = `${context.count}`;
    content.style.lineHeight = size + 'px';
    content.style.color = fontColor;
    content.style.fontSize = '24px';
    if (context.count > 100 && context.count < 1000) {
      content.style.fontSize = '24px';
    } else if (context.count > 10) {
      content.style.fontSize = '20px';
    }
    content.style.textAlign = 'center';
    content.style.fontWeight = '700';
    content.style.background = clusterIconBgColor;
    content.style.border = 'solid 2px ' + clusterIconBorderColor;
    content.style.borderRadius = '50%';
    context.marker.setContent(content);
  }
  // 根据状态获取对应的类名
  private getClassName(item: MapData, deviceType: string): string {
    let status = 'device_'
    console.log(item.devices)
    item.devices.forEach(device => {
      if (device.type == deviceType) {
        status += device.status
      }
    })
    return status;
  }
  // 根据状态获取对应的图标
  private getIconName(item: MapData): string {
    let status = item.status

    let iconName = item.type + '_' + status;

    if (this.gas && item.type != 'device') {
      iconName += '_gas'
    }
    if (item.gpsLost && status == 'normal') {
      iconName += '_gps_lost'
    }
    return iconName
  }
  //尽量显示所有点(市级别缩放)
  public showAllMarkers(): void {

    console.log('-111111111---')
    this.zoom = defaultMapConfig.cityZoom;
    this.mapInstance.setZoom(defaultMapConfig.cityZoom);
  }
  // 当前选中的点居中显示
  public showTheActiveMarker(): void {
    console.log('----')
    this.zoom = defaultMapConfig.streetZoom;
    this.mapInstance.setZoomAndCenter(defaultMapConfig.streetZoom, this.selectedPoint);
  }
}
export interface MapData {
  id: string | number | undefined,
  name?: string;
  center: { longitude?: number, latitude?: number };
  status: 'warning' | 'normal' | 'alarm' | 'fault';
  alarmLevel?: string;
  selected: boolean;
  type: 'member' | 'captain' | 'device';
  devices: DeviceItem[];
  gateway?: 'ttt' | 'smart';
  gasValue: number;
  gpsLost: boolean;
}
export interface TooltipSettings {
  tooltipFontSize: string;
  tooltipFontColor: string;
}
export interface SelectPoint {
  type: 'member' | 'captain' | 'device';
  id: number | string | undefined;
}

