<div class="index-container">
  <!-- 主内容区 -->
  <div class="content">
    <!-- 左侧 列表 -->
    <div class="left-group-list">
      <div class="list-header" odxLayout="flex vertical-center">
        <odx-search-bar>
          <input odxSearchBarControl [(ngModel)]="keyword" (ngModelChange)="setFilter()"
            [placeholder]="'common.search' | translate" />
        </odx-search-bar>
        <app-icon type="built-in" name="evacuation" size="small" iconSet="safety"></app-icon>

      </div>
      <div class="list">
        <map-item-list *ngIf="viewModel == 'group'" [items]="groups" (hideOrOpenGroup)="hideOrOpenGroup($event)"
          (selectItemChanged)="selectItem($event, true)" (selectMemberChanged)="selectMember($event)"> </map-item-list>
        <map-item-list *ngIf="viewModel == 'team'" [items]="teams" (selectItemChanged)="selectItem($event, false)"
          (selectMemberChanged)="selectMember($event)">
        </map-item-list>
      </div>
    </div>
    <!-- 右侧地图 -->
    <div class="right-map">
      <app-device-map [_mapData]="mapData" (selectedUserChanged)="itemInMapSelected($event)" [(viewModel)]="viewModel"
        [(updateMapCenter)]="updateMapCenter" (viewTIC)="viewTIC()"
        (changeViewModel)="changeViewModel()"></app-device-map>
    </div>
  </div>
  <!-- <button class="test-data-btn" (click)="sendTestData()">Test data</button> -->
  <!-- 选中的Group -->
  <div *ngIf="activeItemTpl && !activeMemberItemTpl" class="right-pop">
    <div class="pop-header">
      <div *ngIf="activeItem?.type == 'group' || activeItem?.type == 'team' ">
        <p class="name-text">
          <span [odxTooltip]="activeItem.name">{{activeItem.name}}</span>
        </p>
        <p *ngIf="activeItem?.type == 'group'" class="name-desc">
          <app-icon type="built-in" name="user" size="small"></app-icon>
          <span>{{'common.member' | translate}} {{activeItem.user_count}}</span>
          <app-icon type="built-in" name="bodyguard" size="small" iconSet="safety"></app-icon>
          <span>{{'common.device' | translate}} {{activeItem.device_count}}</span>
        </p>
      </div>
      <app-icon (click)="closeRightPop(true)" class="close-icon" type="built-in" name="chevron-right"></app-icon>
    </div>
    <div class="pop-content" >
      <div *ngIf="activeItem?.type == 'group' || activeItem?.type == 'team' " class="group">
        <nz-collapse [nzBordered]="false">
          <nz-collapse-panel class="member-item" *ngFor="let item of activeItem.subItems" [nzHeader]="nzHeader"
            [nzActive]="true">
            <ng-template #nzHeader>
              <div class="member-item-header">
                <app-icon class="member-icon" *ngIf="item.type == 'captain'" type="svg"
                  name="member_captain"></app-icon>
                <app-icon class="member-icon" *ngIf="item.type == 'member'" type="svg" name="member"></app-icon>
                {{item.name}}
                <odx-chip class="member-item-life" size="small">
                  <app-icon type="built-in" name="lifesignal" iconSet="safety"></app-icon>
                  {{item.heart_count}}
                </odx-chip>
              </div>
            </ng-template>
            <div *ngFor="let device of item.devices">
              <p [ngClass]="{'device-item': true, 'alarm-item':device.status === 'alarm',
      'warning-item':device.status==='warning',}">
                <app-icon class="device-icon" *ngIf="device.type == 'pressure_gauge'" type="built-in" name="scba"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'ttt'" type="built-in" name="communication-tool"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'gas_detector'" type="built-in" name="xam-2000"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'tic'" type="built-in" name="ucf-6000-9000"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'smart_base_station'" type="built-in" name="bodyguard"
                  iconSet="safety"></app-icon>
                {{device.name}}
                <span *ngFor="let telemetry of device.telemetryList; trackBy: trackByTelemetryName" >
                  <odx-chip *ngIf="telemetry.name != 'fallAlarm' && telemetry.name != 'activeAlarm'"  class="device-value" size="small">
                    <span *ngIf="device.type == 'gas_detector'">{{telemetry.name}}</span> {{telemetry.value}} <span
                      *ngIf="device.type == 'pressure_gauge'">{{telemetry.unit}}</span>
                  </odx-chip>
                  <span class="fall-alarm-icon" *ngIf="(telemetry.name == 'fallAlarm' || telemetry.name == 'activeAlarm') && telemetry.status === 'alarm'">
                    <app-icon type="svg" [name]="telemetry.name == 'fallAlarm' ? 'fall_alarm' : 'active_alarm'"></app-icon>
                  </span>
                </span>
              </p>
            </div>
          </nz-collapse-panel>
        </nz-collapse>
      </div>
    </div>
  </div>
  <div *ngIf="activeItemTpl && activeMemberItemTpl" [odxLoadingSpinner]="modalLoading" class="right-pop">
    <div class="pop-header">
      <div>
        <app-icon class="member-icon" *ngIf="activeMember?.type == 'captain'" type="svg"
          name="member_captain"></app-icon>
        <app-icon class="member-icon" *ngIf="activeMember?.type == 'member'" type="svg" name="member"></app-icon>
        <span [odxTooltip]="activeMember?.name">{{activeMember?.name}}</span>
      </div>
      <app-icon (click)="closeRightPop()" class="close-icon" type="built-in" name="chevron-right"></app-icon>
    </div>
    <div class="pop-content">
      <div class="member">
        <nz-collapse [nzBordered]="false">
          <nz-collapse-panel class="device-item" *ngFor="let device of activeMember.devices; trackBy: trackByDeviceId" [nzHeader]="nzHeader"
            [nzActive]="true" [nzDisabled]="device.type == 'ttt' || device.type == 'smart_base_station'">
            <ng-template #nzHeader>
              <p class="device-item-header">
                <app-icon class="device-icon" *ngIf="device.type == 'pressure_gauge'" type="built-in" name="scba"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'ttt'" type="built-in" name="communication-tool"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'gas_detector'" type="built-in" name="xam-2000"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'tic'" type="built-in" name="ucf-6000-9000"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'smart_base_station'" type="built-in" name="bodyguard"
                  iconSet="safety"></app-icon>
                {{device.name}}
                <span *ngFor="let telemetry of device.telemetryList; trackBy: trackByTelemetryName" >
                  <odx-chip *ngIf="telemetry.name != 'fallAlarm' && telemetry.name != 'activeAlarm' && telemetry.value" [ngClass]="{'alarm-item':telemetry.status === 'alarm',
        'warning-item':telemetry.status==='warning'}" class="device-value"
                    size="small">
                    <span *ngIf="device.type == 'gas_detector'">{{telemetry.name}}</span> {{telemetry.value}} 
                    <span *ngIf="device.type == 'pressure_gauge' || device.type == 'ttt'">{{telemetry.unit}}</span>
                  </odx-chip>
                  <span *ngIf="(telemetry.name == 'fallAlarm' || telemetry.name == 'activeAlarm') && telemetry.status === 'alarm'" class="fall-alarm-icon">
                    <app-icon type="svg" [name]="telemetry.name == 'fallAlarm' ? 'fall_alarm_white' : 'active_alarm_white'"></app-icon>
                  </span>
                </span>
              </p>
            </ng-template>
            <div class="device-chart" *ngIf="device.type == 'pressure_gauge' || device.type == 'gas_detector'">
            
              <div [ngClass]="{'alarm-item':telemetry.status === 'alarm',
      'warning-item':telemetry.status==='warning', 'chart-item': true}" *ngFor="let telemetry of device.telemetryList; trackBy: trackByTelemetryName">
                <div class="header-status" *ngIf="telemetry?.status != 'normal'">
                  <app-icon class="status-icon" *ngIf="telemetry?.status ==='warning'" type="built-in" iconSet="safety"
                  [name]="'warning'"></app-icon>
                  <app-icon class="status-icon" *ngIf="telemetry?.status==='alarm' && telemetry?.alarmLevel === 'A1'" type="built-in"
                  iconSet="safety" [name]="'alarm3-a1'"></app-icon>
                  <app-icon class="status-icon" *ngIf="telemetry?.status==='alarm' && telemetry?.alarmLevel === 'A2'" type="built-in"
                  iconSet="safety" [name]="'alarm3-a2'"></app-icon>
                  <span>{{telemetry?.alarmTime}}</span>
                </div>
                <div class="header" odxLayout="flex">
                  <div class="title" odxLayout="flex">
                    <div class="name">{{telemetry.name}}</div>
                    <div class="unit">{{telemetry.unit}}</div>
                  </div>
                  <div class="value">{{telemetry.value ? telemetry.value:'---'}}</div>
                </div>
                <div class="chart" *ngFor="let chart of chartItems; trackBy: trackByChartName">
                  <app-detail-chart *ngIf="chart.name === telemetry.name" [chartHeight]="80" [status]="telemetry.status"
                    [data]="chart.dataSets||[]" [min]="chart.min" [keys]="chart.keys"
                    [max]="chart.max"></app-detail-chart>
                </div>
              </div>
            </div>
            <div *ngIf="device.type == 'tic'">
              <p>
                <span>
                  Temp ℃
                </span>
                <span>
                  {{device.telemetryList.length ? device.telemetryList[device.telemetryList.length - 1].value : 0}}
                </span>
              </p>
              <p></p>
            </div>
          </nz-collapse-panel>
        </nz-collapse>
      </div>
    </div>
  </div>
  <div class="mini-view-tic" *ngIf="miniViewTic" cdkDrag cdkDragBoundary=".index-container">
    <div class="mini-view-header">
      <span>{{ 'common.viewTic' | translate}}</span>
      <div class="action-item">
        <app-icon (click)="viewTIC()" type="built-in" iconSet="safety" name="maximize"></app-icon>
        <app-icon (click)="closeMiniView()" type="built-in" name="close"></app-icon>
      </div>
    </div>
    <div class="mini-view-content">
      <p class="video-title">
        <span>Temp ℃</span>
        <span>35.7</span>
      </p>
    </div>
  </div>
</div>