import {
  Component,
  Input,
  OnDestroy,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Inject
} from '@angular/core';
import { UtilService } from '@services/util.service';
// import { WebsocketService } from '@services/websocket.service';
// import { EchoService } from 'ngx-laravel-echo';
import Echo from 'laravel-echo';
import io from 'socket.io-client';
import { OdxToastCustomService } from '@services/odx-toast-custom.service';
import { TranslateService } from '@ngx-translate/core';
// import * as _ from 'lodash';
import {
  DashboardService,
  MapItem,
  MemberItem
} from '@services/dashboard.service';
import { ContextService } from '@services/context.service';
import {
  MapData,
  SelectPoint,
} from 'src/app/shared/components/device-map/device-map.component';
import {
  SystemTenantService,
  IndexQuerySchema,
  SystemTenantAdminComplex,
  SystemMemberDataService,
  MemberDataControllerGetAllMembersTelemetryData200ResponseDataMembersInner,
  SystemTeamService,
  SystemTeamResource,
  SystemDevice
} from 'src/app/api/backend';
// import moment from 'moment';
import {
  Observable,
  Subscription
} from 'rxjs';
// import { FormGroup, FormControl } from '@angular/forms';
import { SettingsService, User } from '@services/settings.service';
import { environment } from "@env/environment";
import { DA_SERVICE_TOKEN, ITokenService } from "@delon/auth";
// import { LocalStorageService } from './../../../services/local-storage.service';
import { ModalService } from '@odx/angular/components/modal';
// import { DOCUMENT } from '@angular/common';
// import { is } from 'date-fns/locale';
import { ViewTicPopUpComponent } from '../view-tic-pop-up/view-tic-pop-up.component';
import { keys } from 'lodash';
import moment from 'moment';

@Component({
  selector: 'app-dashboard-page',
  templateUrl: './dashboard-page.component.html',
  styleUrls: ['./dashboard-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardPageComponent implements OnDestroy {
  @Input() tenantId?: number;
  public hasAlarms = false;
  public hasWarnings = false;
  public miniViewTic = false;
  activeItemTpl: boolean = false;
  activeMemberItemTpl: boolean = false;
  echoInstance: any;
  channel: string = '';
  accountChannel: string = '';
  controllerId: number | null = null;
  status: string | null = null;
  // 当前选中的设备项
  public activeItem?: MapItem;
  public activeMember?: MemberItem;
  keyword: string = '';
  //  展示的设别数据
  public groups: MapItem[] = [];
  public teams: MapItem[] = [];
  public _initialMapItems: MapItem[] = [];
  public viewModel: string = 'group';
  public initGateway: SystemDevice[] = [];
  public initialGroups: SystemTeamResourceMap[] = [];
  public initialMembers: MemberDataControllerGetAllMembersTelemetryData200ResponseDataMembersInner[] = [];
  // 图表配置项
  public chartItems: ChartItem[] = [];
  // 获取通道数据的时间间隔
  chartIndent = 15;
  // 右侧member详情弹窗
  public modalLoading?: boolean;
  // 地图数据
  mapData: MapData[] = [];
  itemChangeSub: Subscription;
  // 是否更新地图的中心点
  updateMapCenter: boolean = false;
  currentUser: User;
  constructor(
    private _utilService: UtilService,
    // private ws: WebsocketService,
    private _msgSrv: OdxToastCustomService,
    private _translateService: TranslateService,
    private systemTenantService: SystemTenantService,
    private dashboardService: DashboardService,
    private systemTeamService: SystemTeamService,
    private cdRef: ChangeDetectorRef,
    private settingsService: SettingsService,
    private systemMemberDataService: SystemMemberDataService,
    private modalService: ModalService,
    private _contextService: ContextService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    //@Inject(DOCUMENT) private document: Document,
  ) {
    this.currentUser = this.settingsService.User;
    this.tenantId = (this.currentUser.accountable as SystemTenantAdminComplex)?.tenant_id as number ?? 1;
    this.getInitialMembers();
    this.initEcho();

    // 将组件实例暴露到全局，方便调试
    (window as any).dashboardComponent = this;

    // 监听设备列表的变化
    this.itemChangeSub = this.dashboardService.mapItemsChange$.subscribe(
      (item) => {
        this._initialMapItems = item;
        console.log('------- item change events')
        this.setFilter();
        this.updatePageData();
      }
    );

    // of(delay(1000))
    //   .pipe(concatMap(() => this.getInitialTeams()))
    //   .subscribe((res) => {
    //    // this.dashboardService.formatMapData(res.data);
    //    // this.dashboardService.formatMapData(res.data, 'device');
    //   });
  }
  // 初始化larave echo
  async initEcho() {
    let tokenData = this.tokenService.get();
    console.log('🔄 初始化 Echo...');

    if (tokenData?.token && !this.echoInstance) {
      try {
        // 动态导入 Socket.IO v2.x
        console.log('📦 动态加载 Socket.IO...');
        (window as any).io = io;
        this.echoInstance = new Echo({
          broadcaster: 'socket.io',
          host: environment['echoHost'],
          transports: ['websocket'],
          extraHeaders: {
            Authorization: tokenData?.token
          },
          auth: {
            headers: {
              Authorization: tokenData?.token
            }
          }
        });
        console.log('✅ Echo 实例创建成功');

        // 监听连接事件
        this.echoInstance.connector.socket.on('connect', () => {
          console.log('🔗 Socket.IO 连接成功');
        });

        this.echoInstance.connector.socket.on('disconnect', (reason: any) => {
          console.error('❌ Socket.IO 连接断开, 原因:', reason);
          // 尝试重连
          if (reason === 'io server disconnect') {
            console.log('🔄 服务器主动断开，尝试重连...');
            this.echoInstance.connector.socket.connect();
          }
        });

        this.listenTenantChannel();
        // this.listenTenantAccount();
      } catch (error) {
        console.error('❌ Echo 初始化失败:', error);
      }
    } else {
      if (!tokenData?.token) {
        console.warn('⚠️ 无法初始化 Echo: Token 不存在');
      }
      if (this.echoInstance) {
        console.log('ℹ️ Echo 实例已存在，跳过初始化');
      }
    }
  }


  // 监听当前的租户通道
  private listenTenantChannel(): void {
    if (this.echoInstance) {
      this.channel = `tenant.realtime.${this.tenantId}`;
      console.log('📡 准备订阅频道:', this.channel);

      try {
        // 先离开之前的频道（如果存在）
        this.echoInstance.leaveChannel(this.channel);
        const privateChannel = this.echoInstance.private(this.channel);


        privateChannel.listen('.tenant.realtime.updated', (e: any) => {
          console.log(`📨 收到消息!`, e);
          this.parseMessage(e);
        });
      } catch (error) {
        console.error('🚨 设置频道监听失败:', error);
      }
    } else {
      console.warn('⚠️ Echo 实例不存在，无法监听频道');
    }
  }

  // 第一次打开页面通过请求获取team列表
  getInitialTeams(): Observable<{ data: SystemTeamResource[] }> {
    const query: IndexQuerySchema = {
      size: 9999,
      page: 1,
      sort: ['id'],
      filter: [],
      _with: ['members'],
    };
    return this.systemTeamService.teamControllerIndex(query);
  }

  // 第一次打开页面通过请求获取网关列表
  getInitialGateway(): Observable<{ data: SystemTeamResource[] }> {
    const query: IndexQuerySchema = {
      size: 9999,
      page: 1,
      sort: ['id'],
      filter: [],
    };
    return this.systemTenantService.tenantControllerIndexDevice(this.tenantId ?? 1, true, undefined, query);
  }


  // 获取member 列表
  getInitialMembers() {
    const endTime = moment().format('YYYY-MM-DD HH:mm:ss');
    const startTime = moment()
      .subtract(this.chartIndent, 'minutes')
      .format('YYYY-MM-DD HH:mm:ss');
    this.systemMemberDataService.memberDataControllerGetAllMembersTelemetryData(1, startTime, endTime).subscribe((res) => {
      this.initialMembers = res.data.members ? res.data.members : [];
      this.getInitialTeams().subscribe((teamRes) => {
        this.initialGroups = teamRes.data ? teamRes.data : [];
        this.initGroups()
      });
      this.getInitialGateway().subscribe(gateway => {
        this.initGateway = gateway.data ? gateway.data : [];
        this.initTeams()
      })
    })
  }

  private initGroups() {
    let groups: SystemTeamResourceMap[] = [];
    let tempGroups: { [key: number]: any[] } = {};
    this.initialMembers.forEach(member => {
      if (keys(member.device_data).length) {
        if (member.member_info?.team_id) {
          const teamId = member.member_info.team_id;
          if (tempGroups[teamId]) {
            // 处理已存在的团队组
            tempGroups[teamId].push(member);
          } else {
            // 创建新的团队组
            tempGroups[teamId] = [member];
          }
        } else {
          const teamId = 0
          if (tempGroups[teamId]) {
            // 处理已存在的团队组
            tempGroups[teamId].push(member);
          } else {
            // 创建新的团队组
            tempGroups[teamId] = [member];
          }
        }
      }
    })
    this.initialGroups.forEach(team => {
      if (tempGroups[team.id]) {
        groups.push({
          ...team,
          members: tempGroups[team.id],
        } as SystemTeamResourceMap);
      }
    })
    if (tempGroups[0] && tempGroups[0].length) {
      groups.push({
        id: 0,
        name: this.settingsService.lang == 'en_US' ? 'Default Group' : '默认分组',
        members: tempGroups[0],
      } as SystemTeamResourceMap);
    }
    console.log('Group list', groups)
    this.dashboardService.formatMapData(groups, 'group');
  }

  private initTeams() {
    let teams: SystemTeamResourceMap[] = [];
    let tempGroups: { [key: string]: any[] } = {};
    let groupsIds: number[] = []
    this.groups.forEach(item => {
      if (!item.team_select) {
        groupsIds.push(item.id)
      }
    })
    console.log(groupsIds)
    this.initialMembers.forEach(member => {
      if (member.device_data && Object.keys(member.device_data).length) {
        let groupId = member.member_info?.team_id ?? 0
        if (groupsIds.indexOf(groupId) !== -1) {
          // const teamName = member.member_info.team_id;
          console.log(member)
          let keys = Object.keys(member.device_data || {});
          let deviceIdentifier = '';
          if (keys.length) {
            keys.forEach(k => {
              let lastRecord = (member?.device_data as any)?.[k]?.length ? (member?.device_data as any)[k][(member?.device_data as any)[k].length - 1] : null;
              console.log(lastRecord)
              if (k == 'ttt') {
                deviceIdentifier = lastRecord.device_identifier
              } else if (k == 'smart_base_station') {
                deviceIdentifier = lastRecord.device_identifier
              }
            })
          }
          if (deviceIdentifier) {
            let gateWay = this.initGateway.filter(item => item.identifier == deviceIdentifier);
            let teamName = gateWay[0]?.name;
            if (teamName) {
              if (tempGroups[teamName]) {
                // 处理已存在的团队组
                tempGroups[teamName].push(member);
              } else {
                // 创建新的团队组
                tempGroups[teamName] = [member];
              }
            }
          }
        }
      }
    })
    console.log('Team list', tempGroups)
    let keys = Object.keys(tempGroups)
    keys.forEach((team: any) => {
      teams.push({
        id: team,
        name: team,
        members: tempGroups[team],
      } as SystemTeamResourceMap);
    })
    this.dashboardService.formatMapData(teams, 'team');
  }

  private updatePageData(): void {
    this.updateMapData();
    this.updateRender();
  }
  // 更新地图的数据
  private updateMapData(): void {
    // 返回有经纬度的数据
    let itemNeedUpdatePosition: MemberItem[] = [];
    this._initialMapItems.forEach((item) => {
      if (item.type == 'group') {
        if (!item.team_select || this.viewModel == 'group') {
          item.subItems.forEach(member => {
            if (member.center && member.center.length == 2 && member.center[0] && member.center[1]) {
              itemNeedUpdatePosition.push(member);
            }
          })
        }
      } else if (item.type == 'device') {
        let device: MemberItem;
        device = {
          id: item.id,
          member_id: '',
          name: item.name ?? '',
          center: item.center,
          type: item.type,
          status: item.dataStatus,
          heart_count: 0,
          devices: []
        };
        if (device.center) {
          itemNeedUpdatePosition.push(device);
        }
      }
    })
    let needUpdate = false;
    let mapData: MapData[] = [];
    //更新地图定位信息
    // 检查新数据中是否存在初始数据中没有的数据
    const addedItems = itemNeedUpdatePosition.filter(
      (item) => !this.mapData.find((map) => map.id === item.id && map.type === item.type))
    // 有新的设备时需要更新
    if (addedItems.length > 0) {
      needUpdate = true;
      // 地图信息
      mapData = itemNeedUpdatePosition.map((item) => ({
        id: item.id,
        name: item.name,
        status: item.status, // 对应设备的数据状态
        center: { longitude: item.center?.[0], latitude: item.center?.[1] },
        selected: this.activeMember?.id === item.id,
        type: item.type,
        devices: item.devices,
        gateway: item.gateway,
        gasValue: item.gasValue ? item.gasValue : 1,
        gpsLost: item.gpsLost ? item.gpsLost : false
      }));
    }
    if (needUpdate || this.mapData.length != itemNeedUpdatePosition.length) {
      this.mapData = mapData;
    }
  }

  // 用户点击地图上的点选中某一个设备
  public itemInMapSelected(selected: SelectPoint) {
    if (selected.type == 'device') {
      return;
    }

    this.updateMapCenter = true;
    this.mapData.forEach((item) => {
      const isSelected =
        item.id === selected.id && item.type == selected.type;
      if (item.selected !== isSelected) {
        item.selected = isSelected
      }
    });
    if (this.viewModel == 'group') {
      this.groups.forEach(group => {
        group.selected = false;
        group.subItems.forEach(member => {
          const isSelected =
            member.id === selected.id && member.type == selected.type;
          if (member.selected !== isSelected) {
            member.selected = isSelected
          }
          if (isSelected) {
            console.log(isSelected);
            console.log('Selected group:', group);
            group.selected = isSelected
          }

          if (isSelected) {
            this.activeMember = member;
            this.activeItem = group;
            this.activeItemTpl = true;
            // this.activeMemberItemTpl = true;
          }
        })
      })
      this.groups = [...this.groups];
    } else {
      this.teams.forEach(group => {
        group.selected = false;
        group.subItems.forEach(member => {
          const isSelected =
            member.id === selected.id && member.type == selected.type;
          if (member.selected !== isSelected) {
            member.selected = isSelected
          }
          if (isSelected) {
            console.log(isSelected);
            console.log('Selected group:', group);
            group.selected = isSelected
          }

          if (isSelected) {
            this.activeMember = member;
            this.activeItem = group;
            this.activeItemTpl = true;
            // this.activeMemberItemTpl = true;
          }
        })
      })
      this.teams = [...this.teams];
    }
    if (this.activeMember) {
      this.selectMember(this.activeMember)
    }
    // console.log(this.groups)
    // this.updatePageData()
    // console.log('=========')
  }
  // 更新筛选的状态
  public setFilter(): void {
    let groups: MapItem[] = [];
    let teams: MapItem[] = [];
    if (this.keyword) {
      let filterWord = this.keyword.toLocaleLowerCase();
      groups = this.dashboardService.groups.filter(item => {
        return item.identifier.toLocaleLowerCase().includes(filterWord)
      })
      teams = this.dashboardService.teams.filter(item => {
        return item.identifier.toLocaleLowerCase().includes(filterWord)
      })
    } else {
      groups = this.dashboardService.groups;
      teams = this.dashboardService.teams;
    }
    this.groups = this.dashboardService.sortMapItem(groups);
    this.teams = this.dashboardService.sortMapItem(teams);
    console.log(this.groups);
    this.updateMapData();
    this.updateRender();
  }
  public selectItem(item: MapItem, isSelectedGroup: boolean = false) {
    this.activeMemberItemTpl = false;
    if (isSelectedGroup) {
      this.groups.forEach((group) => {
        if (item.id === group.id) {
          group.selected = !group.selected;
          this.activeItemTpl = group.selected
          if (this.activeItemTpl) {
            this.activeItem = group;
          } else {
            this.activeItem = undefined
          }
        } else {
          group.selected = false;
        }
      });
    } else {
      this.teams.forEach((group) => {
        if (item.id === group.id) {
          group.selected = !group.selected;
          this.activeItemTpl = group.selected
          if (this.activeItemTpl) {
            this.activeItem = group;
          } else {
            this.activeItem = undefined
          }
        } else {
          group.selected = false;
        }
      });
    }
  }

  public selectMember(item: MemberItem, isRefresh = true) {
    if (this.viewModel == 'group') {
      this.groups.forEach((group) => {
        if (group.selected) {
          group.subItems.forEach((member) => {
            if (item.id === member.id) {
              member.selected = !member.selected;
              this.activeMember = member;
            } else {
              member.selected = false;
            }
          })
        }
      });
      this.groups = [...this.groups];
    } else {
      this.teams.forEach((group) => {
        if (group.selected) {
          group.subItems.forEach((member) => {
            console.log(member.id)
            console.log(item.id)
            if (item.id === member.id) {
              member.selected = !member.selected;
              this.activeMember = member;
            } else {
              member.selected = false;
            }
          })
        }
      });
    }
    this.mapData.forEach((item) => {
      const isSelected =
        item.id === this.activeMember?.id;
      if (item.selected !== isSelected) {
        item.selected = isSelected;
      }
    });

    if (isRefresh) {
      this.getTheMemberRecord();
    }
    this.activeMemberItemTpl = true;
    this.updateMapData();
    this.updateRender();
  }

  // 获取设备的实时数据
  private getTheMemberRecord() {
    const endTime = moment().format('YYYY-MM-DD HH:mm:ss');
    const startTime = moment()
      .subtract(this.chartIndent, 'minutes')
      .format('YYYY-MM-DD HH:mm:ss');
    const memberIds: number[] = [];
    memberIds.push(Number(this.activeMember?.id));
    this.modalLoading = true
    this.systemMemberDataService.memberDataControllerGetAllMembersTelemetryData(1, startTime, endTime, false, memberIds.join('|'))
      .subscribe({
        next: (res) => {
          let memberInfo: MemberDataControllerGetAllMembersTelemetryData200ResponseDataMembersInner | null = res.data.members ? res.data.members[0] : null;
          console.log(res.data.members)
          this.activeMember = this.dashboardService.formatMember(memberInfo, this.activeMember?.id == memberInfo?.member_info?.id)
          this.modalLoading = false
          let keyStrings = Object.keys(memberInfo?.device_data || {});
          this.chartItems = [];
          keyStrings.forEach(k => {
            if (k == 'gas_detector' || k == 'pressure_gauge') {
              let lastRecord = (memberInfo?.device_data as any)[k][(memberInfo?.device_data as any)[k].length - 1];
              let dataKeys = Object.keys(lastRecord.telemetry || {});
              dataKeys.forEach(key => {
                if ((key.indexOf('Gas') === -1 && k == 'gas_detector') || ((key == 'GaugePressure' || key == 'Temperature') && k == 'pressure_gauge')) {
                  this.chartItems.push({
                    name: key == 'GaugePressure' ? 'Pressure' : key,
                    dataSets: lastRecord.telemetry[key].map((data: any) => Number(data.value)),
                    min: 0,
                    max: 0,
                    keys: lastRecord.telemetry[key].map((data: any) => {
                      const formattedTime = moment(data.measured_at).format('mm:ss');
                      // console.log('🕐 生成时间标签:', {
                      //   原始时间: data.measured_at,
                      //   格式化时间: formattedTime,
                      //   类型: typeof formattedTime
                      // });
                      return formattedTime;
                    })
                  })
                }
              })
              console.log( this.chartItems)
            }
          })

          this.updateRender();
        },
        error: (error) => {
          console.error('❌ API 请求失败:', error);
          this.modalLoading = false
        }
      });
  }
  // 更新右侧设备某一个通道的图表数据（接收到来自广播的设备信息）
  private updateChartItem(e: any): void {
    if (!this.activeMember) {
      return;
    }
    console.log('update chart')
    let keyStrings = Object.keys(e?.device_data || {});
    // this.chartItems = [];
    keyStrings.forEach(k => {
      if (k == 'gas_detector' || k == 'pressure_gauge') {
        let lastRecord = (e?.device_data as any)[k][(e?.device_data as any)[k].length - 1];
        let dataKeys = Object.keys(lastRecord.telemetry || {});
        dataKeys.forEach(key => {
          if ((key.indexOf('Gas') === -1 && k == 'gas_detector') || ((key == 'GaugePressure' || key == 'Temperature') && k == 'pressure_gauge')) {
            let fieldValue = lastRecord.telemetry[key][lastRecord.telemetry[key].length - 1]?.value;
            let dateValue = lastRecord.telemetry[key][lastRecord.telemetry[key].length - 1]?.measured_at;
            let keyValue = moment(dateValue).format('mm:ss');
            let fieldCode = key == 'GaugePressure' ? 'Pressure' : key;
            const chartItem = this.chartItems?.find(
              (chartItem) => chartItem.name === fieldCode
            );
            const chartItemIndex = this.chartItems
              ? this.chartItems.findIndex(
                (chartItem) => chartItem.name === fieldCode
              )
              : -1;
            const previousDataSets = chartItem?.dataSets || [];
            const previousKeys = chartItem?.keys || [];
            previousDataSets.push(fieldValue);
            previousKeys.push(keyValue)
            if (chartItem && chartItemIndex > -1) {
              this.chartItems?.splice(chartItemIndex, 1, {
                ...chartItem,
                dataSets: [...previousDataSets],
                keys: [...previousKeys]
              });
              this.updateRender();
            }
          }
        })
      }
    })
  }

  public hideOrOpenGroup(item: MapItem) {
    this.groups.forEach((group) => {
      if (item.id === group.id) {
        group.team_select = group.team_select;
      }
    });
    this.updateRender();
  }

  public closeRightPop(isGroup = false) {
    if (isGroup) {
      this.groups.forEach((group) => {
        group.selected = false;
        group.subItems.forEach(member => {
          member.selected = false;
        })
      })
      this.teams.forEach((team) => {
        team.selected = false;
        team.subItems.forEach(member => {
          member.selected = false;
        })
      })
      this.clearActiveItem();
    } else {
      this.groups.forEach((group) => {
        if (group.selected) {
          group.subItems.forEach(member => {
            member.selected = false;
          })
        }
      })
      this.teams.forEach((team) => {
        if (team.selected) {
          team.subItems.forEach(member => {
            member.selected = false;
          })
        }
      })
      this.activeMember = undefined;
      this.activeMemberItemTpl = false;
    }

    this.groups = [...this.groups];
    this.teams = [...this.teams];
    this.updateRender();
  }

  // 选中的设备离线的情况下关闭弹窗清掉选中信息
  private clearActiveItem(): void {
    this.activeItem = undefined;
    this.activeMember = undefined;
    this.activeItemTpl = false;
    this.activeMemberItemTpl = false;
  }

  viewTIC(): void {
    console.log('查看TIC');
    this.miniViewTic = false
    const createModal = this.modalService.open(ViewTicPopUpComponent, {
      size: 'large'
    });
    createModal.onClose$.subscribe((res: any) => {
      console.log(res)
      if (res) {
        this.miniViewTic = true;
        // 手动触发变更检测，因为使用了 OnPush 策略
        this.cdRef.markForCheck();
      }
    });
  }

  closeMiniView(): void {
    this.miniViewTic = false;
    // 手动触发变更检测，因为使用了 OnPush 策略
    this.cdRef.markForCheck();
  }
  // 更新页面的渲染
  updateRender(): void {
    this.cdRef.markForCheck();
  }

  parseMessage(e: any) {
    if (e.member_id) {
      const receiveMember = this.initialMembers.find(
        (member) => member.member_info?.member_id === e.member_id
      );
      if (receiveMember) {
        this.initialMembers = this.initialMembers.map(
          (member) => {
            if (member.member_info?.member_id === e.member_id) {
              member.member_data = e.member_data;
              member.device_data = e.device_data
              console.log(member)
              return member;
            }
            return member;
          }
        );
      } else {
        let teamId: number = 0
        let memberName = ''
        let memberId = 0
        this.initialGroups?.forEach(group => {
          if (group.members?.length) {
            group.members.forEach(member => {
              if (member.card_id == e.member_id) {
                memberId = member.id ?? 0;
                memberName = member.name ?? '';
              }
            })
          }
        })
        if (memberId) {
          let memberInfo: MemberDataControllerGetAllMembersTelemetryData200ResponseDataMembersInner = {
            member_info: {
              id: memberId,
              member_id: e.member_id,
              name: memberName,
              tenant_id: e.tenant_id,
              team_id: teamId
            },
            member_data: e.member_data,
            device_data: e.device_data
          }
          this.initialMembers.push(memberInfo);
        }
      }

      this.viewModel == 'group' ? this.initGroups() : this.initTeams();
      if (this.activeItem) {
        this.selectItem(this.activeItem, this.viewModel == 'group');
      }
      if (this.activeMember) {
        if (this.activeMember.member_id == e.member_id) {

          this.updateChartItem(e)
        }
        this.selectMember(this.activeMember, false);
      }
      console.log('Receive message and update group data')
      console.log(this.activeItem)
      console.log(this.activeMember)
      this.updatePageData();
    }
  }

  changeViewModel() {
    this.viewModel = this.viewModel === 'group' ? 'team' : 'group';
    if (this.activeMember) {
      this.closeRightPop();
    }
    if (this.activeItem) {
      this.closeRightPop(true)
    }
    if (this.viewModel == 'team') {
      this.initTeams()
    } else {
      this.initGroups()
    }
  }
  /**
   * trackBy 函数优化 ngFor 性能
   */
  trackByChartName(index: number, chart: any): string {
    return `${this.activeItem?.identifier}_chart_name_${chart?.name}`;
  }

  trackByTelemetryName(index: number, telemetry: any): string {
    return `${this.activeItem?.identifier}_telemetry_name_${telemetry?.name}`;
  }


  trackByDeviceId(index: number, device: any): number | string {
    return `${this.activeItem?.identifier}_devicename_${device?.type}`;
  }

  ngOnDestroy(): void {
    console.log('🧹 组件销毁，清理 Echo 连接...');

    // 取消订阅
    if (this.itemChangeSub) {
      this.itemChangeSub.unsubscribe();
      console.log('✅ 已取消 itemChangeSub 订阅');
    }

    // 清理 Echo 连接
    if (this.echoInstance) {
      try {
        this.echoInstance.leaveAllChannels(this.channel);
        console.log('🚪 已离开频道:', this.channel);


        this.echoInstance.disconnect();
        console.log('🔌 Echo 连接已断开');

        this.echoInstance = null;
      } catch (error) {
        console.error('❌ 清理 Echo 连接时出错:', error);
      }
    }
  }
}

interface ChartItem {
  name: string | undefined;
  dataSets?: number[];
  keys?: string[];
  min?: number;
  max?: number;
}

interface SystemMemberResourceMap extends MemberDataControllerGetAllMembersTelemetryData200ResponseDataMembersInner {
  card_id?: string;
  name?: string;
  id?: number;
}
export interface SystemTeamResourceMap extends SystemTeamResource {
  members?: SystemMemberResourceMap[];
}
