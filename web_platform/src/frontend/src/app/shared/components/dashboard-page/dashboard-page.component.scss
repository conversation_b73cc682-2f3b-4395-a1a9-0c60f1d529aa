@import "../../../../styles.scss";

.index-container {
  position: relative;

  .content {
    width: 100%;
    overflow: hidden;
    display: flex;

    .left-group-list {
      height: 100%;
      background-color: var(--odx-c-background-content);
      width: calc($marginBase * 300 /24);
      min-width: min(100dvw, 180px);
      position: relative;

      .list-header {
        justify-content: space-between;
        align-items: center;
        padding: calc($marginBase * 12 / 24) calc($marginBase* 16 / 24);
        font-size: calc($baseFont * 16 / 24);
        height: calc($marginBase * 70 / 24);

        .action-btn {
          color: var(--desc-text-color);
          padding-right: calc($marginBase * 6 / 24);
          margin-left: calc($marginBase * -10 / 24);

          ::ng-deep .odx-icon {
            font-size: calc($baseFont * 24 / 24);
          }
        }

        ::ng-deep {
          .odx-search-bar {
            display: flex;
            align-items: center;
            flex-grow: 1;
          }

          .odx-search-bar,
          .odx-search-bar__input {
            line-height: calc($marginBase * 36 / 24);
            font-size: calc($baseFont * 16 / 24);
          }

          .odx-icon {
            font-size: calc($baseFont * 24 / 24);
          }
        }
      }

      .list {
        height: calc(100vh - $marginBase * 118 / 24);
        overflow: scroll;

        ::ng-deep .ant-collapse {
          background: #fff;
          border: none;
        }

        ::ng-deep .ant-collapse-header {
          padding: calc($marginBase * 16 / 24) calc($marginBase * 12 / 24) calc($marginBase * 16 / 24) calc($marginBase * 4 / 24) !important;
          color: var(--odx-c-text);
          font-size: calc($baseFont * 14 / 24);
          line-height: calc($baseFont * 18 / 24) !important;
          letter-spacing: 2px;
        }

        ::ng-deep .ant-collapse-content {
          background: var(--map-item-active-bg-color);
        }
      }
    }

    .right-map {
      flex: 1;
    }
  }

  .right-pop {
    position: absolute;
    top: calc($marginBase * 16 /24);
    right: calc($marginBase * 16 /24);
    width: calc($marginBase * 300 /24);
    height: calc(100% - $marginBase * 32 / 24);
    background: #fff;
    border-radius: calc($marginBase * 5 / 24);

    .pop-header {
      padding: calc($marginBase * 16 / 24);
      font-size: calc($baseFont * 20 / 24);
      font-weight: 600;
      font-family: var(--semiBoldFont);
      line-height: calc($marginBase * 25 / 24);
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: "DraegerPangeaText-SemiBold";

      .name-text {
        font-family: "DraegerPangeaText-SemiBold";
      }

      .name-desc {
        font-size: calc($baseFont * 12 / 24);
        font-family: var(--semiBoldFont);
        line-height: calc($marginBase * 19 / 24);
        font-family: "DraegerPangeaText-Regular";
        font-weight: 400;

        ::ng-deep .odx-icon {
          font-size: calc($baseFont * 16 / 24);
        }

        span {
          display: inline-block;
          color: var(--odx-chip-color);
          line-height: calc($baseFont * 16 / 24);
          margin-right: calc($marginBase * 10 / 24);
        }
      }

      .close-icon {
        cursor: pointer;

        ::ng-deep .odx-icon {
          font-size: calc($baseFont * 24 / 24);
        }
      }

      .member-icon {
        ::ng-deep .odx-icon {
          font-size: calc($baseFont * 40 / 24);
        }
      }
    }

    .pop-content {
      height: calc(100% - $marginBase * 76 / 24);
      overflow: scroll;
    }
  }
}

.ant-collapse-borderless>.ant-collapse-item {
  border-bottom: 1px solid var(--device-detail-border-color);
}

/* group详情弹出框样式 */
.group {
  border-top: 1px solid rgba(0, 39, 102, 0.12);
  padding: calc($marginBase * 16 / 24);
  background: #fff;

  ::ng-deep .ant-collapse-content,
  ::ng-deep .ant-collapse-borderless {
    background: #fff;
    border-radius: calc($marginBase * 8 / 24);
  }

  ::ng-deep .ant-collapse-item,
  ::ng-deep .ant-collapse-content-box {
    background: var(--gray-50);
    overflow: hidden;
    padding: 0;
  }

  .member-item {
    margin-bottom: calc($marginBase * 8 / 24);
    border-radius: calc($marginBase * 8 / 24);
    border: 1px solid var(--device-detail-border-color);

    .member-item-header {
      font-size: calc($baseFont * 14 / 24);
      font-weight: 600;
      font-family: "DraegerPangeaText-SemiBold";

      .member-icon {
        ::ng-deep .odx-icon {
          font-size: calc($baseFont * 26 / 24);
        }
      }

      .member-item-life {
        display: inline-block;
        margin-left: calc($marginBase * 6 / 24);
        background: var(--blue-50);
        line-height: calc($baseFont * 16 / 24);
        color: var(--blue-400);
        font-weight: normal;
        font-family: "DraegerPangea-Medium";

        ::ng-deep .odx-icon {
          font-size: calc($baseFont * 16 / 24);
          color: var(--odx-chip-color);
        }
      }
    }

    .device-item {
      padding: calc($marginBase * 12 / 24) calc($marginBase * 8 / 24);
      border-top: 1px solid var(--device-detail-border-color);
      font-size: calc($baseFont * 14 / 24);
      line-height: calc($baseFont * 18 / 24);
      color: var(--blue-700);
      font-family: "DraegerPangea-Medium";

      .device-icon {
        ::ng-deep .odx-icon {
          font-size: calc($baseFont * 20 / 24);
        }
      }

      .device-value {
        line-height: calc($baseFont * 16 / 24);
        color: var(--blue-400);
        background: var(--blue-50);
        margin-left: calc($marginBase * 4 / 24);
      }
      .fall-alarm-icon {
        margin-left: 5px!important;
      }
    }

    .alarm-item {
      background: var(--device-alarm-color);
      color: #fff;

      ::ng-deep .odx-icon {
        color: #fff;
      }

      .device-value {
        background: #fff;
        color: var(--device-alarm-color);
      }
    }

    .warning-item {
      background: var(--device-error-color);

      .device-value {
        background: var(--blue-700);
        color: var(--device-error-color);
      }
    }

  }
}

/* group详情弹出框样式 */
.member {
  border-top: 1px solid rgba(0, 39, 102, 0.12);
  padding: calc($marginBase * 12 / 24);
  background: #fff;

  ::ng-deep .ant-collapse-header {
    padding-left: calc($marginBase * 8 / 24) !important;
    padding-right: calc($marginBase * 8 / 24) !important;
  }

  ::ng-deep .ant-collapse-content,
  ::ng-deep .ant-collapse-borderless {
    background: #fff;
    border-radius: calc($marginBase * 8 / 24);
  }

  ::ng-deep .ant-collapse-item-disabled .ant-collapse-arrow {
    display: none;
  }

  ::ng-deep .ant-collapse-item-disabled .ant-collapse-header {
    padding-left: calc($marginBase * 32 / 24) !important;
  }

  ::ng-deep .ant-collapse-item,
  ::ng-deep .ant-collapse-content-box {
    background: var(--gray-50);
    overflow: hidden;
    padding: 0;
  }

  .device-item {
    margin-bottom: calc($marginBase * 8 / 24);
    border-radius: calc($marginBase * 8 / 24);

    .device-item-header {
      font-size: calc($baseFont * 14 / 24);
      font-weight: 600;
      color: var(--blue-700);
      font-family: "DraegerPangeaText-SemiBold";

      .device-icon {
        ::ng-deep .odx-icon {
          font-size: calc($baseFont * 26 / 24);
        }
      }

      .device-value {
        font-family: "DraegerPangea-Medium";
        font-weight: 500;
        color: var(--blue-400);
        background: var(--blue-50);
        margin-left: calc($marginBase * 4 / 24);
      }

      .fall-alarm-icon {
        margin-left: 5px!important;
        padding: 2px 0 !important;
        border-radius: 12px;
      }
      .alarm-item {
        background: var(--device-alarm-color);
        color: #fff;

        ::ng-deep .odx-chip {
          color: #fff;
        }
      }

      .warning-item {
        background: var(--device-error-color);
      }
    }

    .chart-item {
      padding: calc($marginBase * 14 /24) 0 calc($marginBase*4/ 24);
      margin-bottom: $indent-10;
      font-size: calc($baseFont * 18 / 24);

      .header {
        font-weight: 600;
        font-family: var(--semiBoldFont);
        justify-content: space-between;
        padding: 0 calc($marginBase * 10 / 24);

        .unit {
          font-family: var(--odx-typography-font-family);
          color: #758BA2;
          opacity: 0.6;
          font-weight: 400;
        }

        .value {
          font-family: var(--semiBoldFont);
          font-weight: 600;
          font-size: $baseFont;
        }
      }
    }

    .alarm-item {
      background-color: var(--device-alarm-color-even);

      .header {
        color: #fff;

        .unit {
          color: #fff;
        }
      }
    }

    .warning-item,
    .fault-item {
      background-color: var(--device-error-color-even);

      .header {
        color: #002766;

        .unit {
          color: #002766;
        }
      }
    }

  }
}

::ng-deep {
  .odx-modal--xsmall {
    width: calc($marginBase * 300 /24)
  }
}


.mini-view-tic {
  position: absolute;
  z-index: 999;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: calc($marginBase * 300 / 24);
  border-radius: calc($marginBase * 6/ 24);
  background: rgba(245, 247, 250, 1);
  box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.3);
  color: var(--blue-700);
  font-weight: 600;

  .mini-view-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 39, 102, 0.05);
    padding: calc($marginBase * 14 / 24) calc($marginBase * 14 / 24);

    .action-item {
      ::ng-deep .odx-icon {
        margin: 0 calc($marginBase * 6 / 24);
        font-size: calc($baseFont * 24 / 24);
      }
    }
  }

  .mini-view-content {
    min-height: calc($marginBase * 200 / 24);
    padding: calc($marginBase * 12 / 24);

    .video-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: calc($marginBase * 8 / 24);
      margin-bottom: calc($marginBase * 10 / 24);
    }
  }
}

.test-data-btn {
  position: fixed;
  top: 100px;
  right: 500px;
  background: green;
}