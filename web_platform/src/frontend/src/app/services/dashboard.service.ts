import { Injectable } from '@angular/core';
import {
  SystemDeviceResource,
  SystemEventsDeviceTelemetryValuePushData,
  SystemDeviceService, SystemTenantService,
  MemberDataControllerGetAllMembersTelemetryData200ResponseDataMembersInner
} from '../api/backend';

import { ReplaySubject } from 'rxjs';
import { SystemTeamResourceMap } from '../shared/components/dashboard-page/dashboard-page.component'

import { formatDuration } from 'src/app/shared/utils/constant';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  public mapItems: MapItem[] = [];
  public groups: MapItem[] = [];
  public teams: MapItem[] = [];
  public devices: MapItem[] = [];
  public alarmDevices: AlarmDevice[] = [];
  mapItemsChange$: ReplaySubject<MapItem[]> = new ReplaySubject<MapItem[]>();
  alarmStatusChange$: ReplaySubject<string> = new ReplaySubject<string>();

  constructor(
    private _systemDeviceService: SystemDeviceService,
    private _systemTenantService: SystemTenantService,
  ) {
  }
  // 处理在线设备的数据格式转换
  public formatMapData(data: SystemTeamResourceMap[], type = 'group') {
    if (type == 'group') {
      const groups = data.map(team => {
        const mapItem = this.formatGroup(team, true);
        return mapItem;
      });
      // console.log('-----')
      // console.log(groups)
      this.groups = groups
    }
    if (type == 'team') {
      const teams = data.map(team => {
        const mapItem = this.formatGroup(team, false);
        return mapItem;
      });
      // console.log('-----')
      // console.log(teams)
      this.teams = teams
    }
    if (type == 'device') {
      const devices = data.map(device => {
        const mapItem = this.formatDevice(device);
        return mapItem;
      });
      this.devices = devices;
    }
    this.mapItemsChange$.next(this.groups.concat(this.devices, this.teams) || []);
  }
  // 对设备进行排序
  public sortMapItem(onlineDevice: MapItem[]): MapItem[] {
    const statusWeight = {
      alarm: 1,
      warning: 2,
      normal: 3,
    };
    return onlineDevice?.sort((mapItemA, mapItemB) => {
      const mapItemAWeight = mapItemA && statusWeight[mapItemA.dataStatus];
      const mapItemBWeight = mapItemB && statusWeight[mapItemB.dataStatus];
      if (mapItemAWeight && mapItemBWeight && mapItemAWeight !== mapItemBWeight) {
        return mapItemAWeight - mapItemBWeight;
      } else {
        return mapItemA.id - mapItemB.id;
      }
    });
  }

  public formatDevice(device: SystemDeviceResource, _updateValue?: SystemEventsDeviceTelemetryValuePushData): MapItem {
    const center = [Number(device.longitude), Number(device.latitude)] as [number, number];
    let dataStatus: 'warning' | 'normal' | 'alarm' = 'normal'
    let hasAlarm = true;
    let hasError = false;
    if (hasAlarm) {
      dataStatus = 'alarm'
    } else if (hasError) {
      dataStatus = 'warning'
    }
    const mapItem: MapItem = {
      id: device.id,
      identifier: device.identifier ?? '',
      name: device.name ?? '',
      dataStatus: dataStatus,
      selected: false,
      team_select: false,
      type: 'device',
      user_count: 0,
      device_count: 0,
      center: center,
      subItems: []
    };
    return mapItem;
  }
  // 格式化group信息用以展示
  public formatGroup(group: SystemTeamResourceMap, isGroup: boolean): MapItem {
    // 处理设备的定位信息
    let status: 'warning' | 'normal' | 'alarm' = 'normal';

    let subItems: MemberItem[] = [];
    let userCount = 0;
    let deviceCount = 0;

    group?.members?.forEach(item => {
      let memberItem = this.formatMember(item)
      deviceCount += memberItem.devices?.length
      subItems.push(memberItem);
    })
    userCount = subItems.length;
    subItems.forEach(member => {
      if (member.status == 'alarm') {
        status = 'alarm';
      } else if (member.status == 'warning' && status != 'alarm') {
        status = 'warning';
      }
    })
    const mapItem: MapItem = {
      id: group.id,
      identifier: group.name ?? '',
      name: group.name ?? '',
      dataStatus: status,
      selected: false,
      team_select: false,
      type: isGroup == true ? 'group' : 'team',
      user_count: userCount,
      device_count: deviceCount,
      subItems: subItems
    };
    // console.log(isGroup)
    // console.log('init group or team', isGroup == true)
    // console.log(mapItem)
    return mapItem;
  }

  public formatMember(memberInfo: MemberDataControllerGetAllMembersTelemetryData200ResponseDataMembersInner | null, isSelected = false): MemberItem {
    const devices: DeviceItem[] = [];
    let memberStatus: 'warning' | 'normal' | 'alarm' = 'normal';
    let longitude = 0;
    let latitude = 0;
    let gasValue = 0;
    let icon = '';
    let gateway: 'ttt' | 'smart' | undefined = undefined;
    const type: 'member' | 'captain' | 'device' = 'member';
    let fallAlarm = false
    let activeAlarm = false
    let center;
    if (memberInfo?.member_data) {
      longitude = (memberInfo.member_data as any)['Longitude'] ? (memberInfo.member_data as any)['Longitude'][(memberInfo.member_data as any)['Longitude'].length - 1]?.value : 0;
      latitude = (memberInfo.member_data as any)['Latitude'] ? (memberInfo.member_data as any)['Latitude'][(memberInfo.member_data as any)['Latitude'].length - 1]?.value : 0;
      fallAlarm = (memberInfo.member_data as any)['FallAlarmSignal'] ? (memberInfo.member_data as any)['FallAlarmSignal'][(memberInfo.member_data as any)['FallAlarmSignal'].length - 1]?.value : false;
      activeAlarm = (memberInfo.member_data as any)['ActiveAlarmSignal'] ? (memberInfo.member_data as any)['ActiveAlarmSignal'][(memberInfo.member_data as any)['ActiveAlarmSignal'].length - 1]?.value : false;
      center = this.convertLonAndLat(Number(longitude), Number(latitude));
    }
    let heartRecords = 0;
    if (memberInfo?.device_data?.wristband) {
      let wristbandData = memberInfo.device_data?.wristband?.length ? memberInfo.device_data?.wristband[memberInfo.device_data?.wristband?.length - 1] : null;
      if (wristbandData) {
        let telemetry = wristbandData.telemetry;
        heartRecords = telemetry && (telemetry as any)['HeartRate'] && (telemetry as any)['HeartRate'].length ? (telemetry as any)['HeartRate'][(telemetry as any)['HeartRate'].length - 1]?.value : 0;
      }
    }
    let keyStrings = Object.keys(memberInfo?.device_data || {});
    if (keyStrings.length) {
      keyStrings.forEach(k => {
        icon = '';
        gasValue = 0;
        if (k != 'wristband') {
          let name = '';
          let telemetryItems: TelemetryItem[] = [];
          let status: 'warning' | 'normal' | 'alarm' = 'normal';
          let lastData: any = null;
          let lastRecord = (memberInfo?.device_data as any)?.[k]?.length ? (memberInfo?.device_data as any)[k][(memberInfo?.device_data as any)[k].length - 1] : null;
          if (k == 'gas_detector') {
            name = 'PAM';
            let dataKeys = Object.keys(lastRecord.telemetry || {});
            dataKeys.forEach(key => {
              if (key.indexOf('Gas') === -1) {
                lastData = lastRecord.telemetry[key][lastRecord.telemetry[key].length - 1];
                let channelStatus: 'warning' | 'normal' | 'alarm' = 'normal';
                if (lastData?.fault) {
                  channelStatus = 'warning'
                }
                if (lastData?.alarm) {
                  channelStatus = 'alarm'
                }
                const durationValue = this.getDurationValue();

                telemetryItems.push({
                  name: key,
                  measured_at: lastData ? lastData.measured_at : '',
                  unit: lastData ? lastData.unit : '',
                  value: lastData ? lastData.value : '',
                  status: channelStatus,
                  alarmLevel: channelStatus == 'alarm' ? (lastData?.alarm > 2 ? 'A2' : 'A1') : 'normal',
                  alarmTime: durationValue > 30 ? formatDuration(durationValue) : '< 30s',
                })
                if (status != 'alarm') {
                  status = channelStatus == 'alarm' ? 'alarm' : (status == 'warning' ? 'warning' : channelStatus);
                }
                icon = lastRecord.metadata.DetectorName ?? null;
              }
            })
            // let lastData = lastRecord.telemetry.GasReadings.length ? lastRecord.telemetry.GasReadings[lastRecord.telemetry.GasReadings.length - 1] : null;
          } else if (k == 'pressure_gauge') {
            name = 'BA';
            let dataKeys = Object.keys(lastRecord.telemetry || {});
            dataKeys.forEach(key => {
              if (key == 'GaugePressure' || key == 'Temperature') {
                lastData = lastRecord.telemetry[key].length ? lastRecord.telemetry[key][lastRecord.telemetry[key].length - 1] : null;
                let channelStatus: 'warning' | 'normal' | 'alarm' = 'normal';
                if (lastData?.fault) {
                  channelStatus = 'warning'
                }
                if (lastData?.alarm) {
                  channelStatus = 'alarm'
                }
                telemetryItems.push({
                  name: key == 'GaugePressure' ? 'Pressure' : key,
                  measured_at: lastData ? lastData.measured_at : '',
                  unit: lastData ? lastData.unit : '',
                  value: lastData ? lastData.value : '',
                  status: channelStatus,
                  alarmLevel: 'normal',
                  alarmTime: ''
                })
                if (status != 'alarm') {
                  status = channelStatus == 'alarm' ? 'alarm' : (status == 'warning' ? 'warning' : channelStatus)
                }
                if (key == 'GaugePressure') {
                  icon = lastRecord.metadata.PressureGaugeType == 1 ? 'scba' : 'scba2';
                  let value = (lastData.value && Number(lastData.value) > 0) ? Number(lastData.value) : 0
                  gasValue = 1 - (lastRecord.metadata.PressureGaugeType == 1 ? value / 20 : value / 30);
                }
              }
            })
          } else if (k == 'tic') {
            name = 'TIC';
            lastData = null; // lastRecord.telemetry?.GaugePressure.length ? lastRecord.telemetry.GaugePressure[lastRecord.telemetry.GaugePressure.length - 1] : null;
            telemetryItems.push({
              name: '',
              measured_at: lastData ? lastData.measured_at : '',
              unit: lastData ? lastData.unit : '',
              value: lastData ? lastData.value : '',
              status: lastData?.alarm ? 'alarm' : 'normal',
              alarmLevel: 'normal',
              alarmTime: ''
            })
            status = lastData?.alarm ? 'alarm' : 'normal';
          } else if (k == 'ttt') {
            gateway = 'ttt';
            name = 'TTT';
            lastData = lastRecord.telemetry?.Temperature?.length ? lastRecord.telemetry.Temperature[lastRecord.telemetry.Temperature.length - 1] : null;
            telemetryItems.push({
              name: '',
              measured_at: lastData ? lastData.measured_at : '',
              unit: lastData ? lastData.unit : '',
              value: lastData ? lastData.value : '',
              status: lastData?.alarm ? 'alarm' : 'normal',
              alarmLevel: 'normal',
              alarmTime: ''
            })

            status = lastData?.alarm ? 'alarm' : 'normal';
            telemetryItems.push({
              name: 'fallAlarm',
              measured_at: '',
              unit: '',
              value: '',
              status: !fallAlarm ? 'alarm' : 'normal',
              alarmLevel: 'normal',
              alarmTime: ''
            })
            
            telemetryItems.push({
              name: 'activeAlarm',
              measured_at: '',
              unit: '',
              value: '',
              status: !activeAlarm ? 'alarm' : 'normal',
              alarmLevel: 'normal',
              alarmTime: ''
            })
            if (status != 'alarm') {
              status = !fallAlarm || !activeAlarm ? 'alarm' : 'normal';
            }
          } else if (k == 'smart_base_station') {
            gateway = 'smart';
            name = 'Smart'
            lastData = lastRecord.telemetry?.Temperature?.length ? lastRecord.telemetry.Temperature[lastRecord.telemetry.Temperature.length - 1] : null;
            telemetryItems.push({
              name: '',
              measured_at: lastData ? lastData.measured_at : '',
              unit: lastData ? lastData.unit : '',
              value: lastData ? lastData.value : '',
              status: lastData?.alarm ? 'warning' : 'normal',
              alarmLevel: 'normal',
              alarmTime: ''
            })
            status = lastData?.alarm ? 'alarm' : 'normal';
          }
          devices.push({
            name: name,
            type: k,
            telemetryList: telemetryItems,
            status: status,
            icon: icon
          })
        }
      })
    }
    devices.forEach(device => {
      if (device.status == 'alarm') {
        memberStatus = 'alarm';
      } else if (device.status == 'warning' && memberStatus != 'alarm') {
        memberStatus = 'warning';
      }
    })
    let member: MemberItem = {
      name: memberInfo?.member_info?.name ?? '',
      id: memberInfo?.member_info?.id ?? '',
      member_id: memberInfo?.member_info?.member_id ?? '',
      status: fallAlarm || activeAlarm ? 'alarm' : memberStatus,
      selected: isSelected,
      heart_count: heartRecords,
      type: type,
      center: center,
      longitude: longitude,
      latitude: latitude,
      devices: devices,
      gateway: gateway,
      gasValue: gasValue
    }
    console.log('member init data')
    console.log(member)
    return member;
  }

  /**
   * GPS坐标转高德地图坐标 (WGS84 -> GCJ02)
   * @param longitude WGS84经度
   * @param latitude WGS84纬度
   * @returns [高德经度, 高德纬度]
   */
  public convertLonAndLat(longitude: number, latitude: number): [number, number] {
    // 检查坐标有效性
    if (!this.isValidCoordinate(longitude, latitude)) {
      console.warn('⚠️ 无效的GPS坐标:', { longitude, latitude });
      return [longitude, latitude];
    }

    // 检查是否在中国境内，境外不需要转换
    if (!this.isInChina(longitude, latitude)) {
      console.log('🌍 坐标在中国境外，无需转换');
      return [longitude, latitude];
    }

    // WGS84 转 GCJ02 (火星坐标系)
    const [gcjLon, gcjLat] = this.wgs84ToGcj02(longitude, latitude);

    console.log('🗺️ 坐标转换:', {
      原始: [longitude, latitude],
      转换后: [gcjLon, gcjLat]
    });

    return [gcjLon, gcjLat];
  }

  /**
   * WGS84坐标系转GCJ02坐标系 (GPS转火星坐标)
   */
  private wgs84ToGcj02(lng: number, lat: number): [number, number] {
    const a = 6378245.0; // 长半轴
    const ee = 0.00669342162296594323; // 偏心率平方

    let dLat = this.transformLat(lng - 105.0, lat - 35.0);
    let dLng = this.transformLng(lng - 105.0, lat - 35.0);

    const radLat = (lat / 180.0) * Math.PI;
    let magic = Math.sin(radLat);
    magic = 1 - ee * magic * magic;
    const sqrtMagic = Math.sqrt(magic);

    dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtMagic)) * Math.PI);
    dLng = (dLng * 180.0) / ((a / sqrtMagic) * Math.cos(radLat) * Math.PI);

    const mgLat = lat + dLat;
    const mgLng = lng + dLng;

    return [mgLng, mgLat];
  }

  /**
   * 纬度转换
   */
  private transformLat(lng: number, lat: number): number {
    let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * 经度转换
   */
  private transformLng(lng: number, lat: number): number {
    let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lng * Math.PI) + 40.0 * Math.sin(lng / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (150.0 * Math.sin(lng / 12.0 * Math.PI) + 300.0 * Math.sin(lng / 30.0 * Math.PI)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * 检查坐标是否有效
   */
  private isValidCoordinate(lng: number, lat: number): boolean {
    return lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90;
  }

  /**
   * 判断坐标是否在中国境内
   */
  private isInChina(lng: number, lat: number): boolean {
    // 中国大陆范围 (粗略判断)
    return lng >= 72.004 && lng <= 137.8347 && lat >= 0.8293 && lat <= 55.8271;
  }

  public updateData() {
    this.mapItemsChange$.next(this.groups.concat(this.devices, this.teams) || []);
  }

  // 获取报警持续时间
  public getDurationValue(): number {
    // const alarmItem = (device as MapItem).alarmContent?.find(item => item.name === telemetry.field_code);
    // let duration = (alarmItem && alarmItem.duration && alarmItem.duration >= 0) ? alarmItem.duration : 0 || telemetry.duration || 0;
    // if (alarmItem?.alarmTimeUpdateAt) {
    //   const current = new Date().getTime();
    //   const space = ((current - alarmItem.alarmTimeUpdateAt.getTime()) / 1000).toFixed(0);
    //   duration = duration + Number(space);
    // }
    return 1;
  }
}
export interface MapItem {
  id: number;
  identifier: string;
  name?: string;
  dataStatus: 'warning' | 'normal' | 'alarm';
  selected?: boolean;
  team_select?: boolean;
  type: 'group' | 'device' | 'team';
  center?: [number, number];//地图定位
  user_count?: number;
  device_count?: number;
  subItems: MemberItem[];
  gateway?: 'ttt' | 'smart';
}

export interface MemberItem {
  name: string;
  id: string | number | undefined;
  member_id: string | number;
  status: 'warning' | 'normal' | 'alarm';
  selected?: boolean;
  heart_count: number;
  type: 'member' | 'captain' | 'device';
  center?: [number, number];//地图定位
  longitude?: number;
  latitude?: number;
  devices: DeviceItem[];
  gateway?: 'ttt' | 'smart';
  gasValue?: number;
}

export interface DeviceItem {
  type: string,
  status: 'warning' | 'normal' | 'alarm' | 'offline';
  telemetryList: TelemetryItem[],
  name?: string;
  icon?: string | null;
}

interface TelemetryItem {
  name?: string;
  measured_at: string;
  unit: string;
  value: string | number | boolean;
  status: 'warning' | 'normal' | 'alarm' | 'offline';
  alarmLevel: 'A1' | 'A2' | 'normal';
  alarmTime: string;
}


interface AlarmDevice {
  deviceId: number;
  buildingId: number;
  status: 'warning' | 'normal' | 'alarm';
  value: string,
  field: string
}
