import { Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { SettingsService } from '@services/settings.service';
@Component({
  selector: 'app-root',
  template: `<router-outlet></router-outlet> `,
})
export class AppComponent {
  title = 'Drager';
  constructor(
    public translate: TranslateService,
    public iconRegistry: MatIconRegistry,
    public sanitizer: DomSanitizer,
    private settingsService: SettingsService,
  ) {
    this.initTranslate();
    this.initSvgIcon();
  }
  public initTranslate(): void {
    this.translate.addLangs(['en', 'zh_CN']);
    this.translate.setDefaultLang(this.settingsService.lang);
    document.body.classList.toggle('zh_CN', this.settingsService.lang == 'zh_CN');
    this.translate.onLangChange.subscribe(({ lang }) => {
      window.location.reload();
    });
  }

  public initSvgIcon(): void {
    this.iconRegistry.addSvgIcon('fullscreen',this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/full-screen.svg'));
    this.iconRegistry.addSvgIcon('fullscreen-exit',this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/fullscreen-exit.svg'));
    this.iconRegistry.addSvgIcon('update_header', this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/update.svg'));
    this.iconRegistry.addSvgIcon('dark_mode', this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/dark_mode.svg'));
    this.iconRegistry.addSvgIcon('light_mode', this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/light_mode.svg'));
    this.iconRegistry.addSvgIcon('help', this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/help.svg'));
    this.iconRegistry.addSvgIcon('overview',this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/overview.svg'));
    this.iconRegistry.addSvgIcon('lang_switch', this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/lang_switch.svg'));
    this.iconRegistry.addSvgIcon('avatar_default', this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/avatar_default.svg'));
    this.iconRegistry.addSvgIcon('member', this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/member.svg'));
    this.iconRegistry.addSvgIcon('member_captain', this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/member_captain.svg'));
    this.iconRegistry.addSvgIcon('union', this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/union.svg'));    
    this.iconRegistry.addSvgIcon('fall_alarm', this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/fall_alarm.svg'));
    this.iconRegistry.addSvgIcon('active_alarm', this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/icons/active_alarm.svg'));

  }

}
